<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">Bank Accounts</h2>
            <a href="{{ route('bank-accounts.create') }}" class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700">NEW BANK ACCOUNT</a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <th class="px-6 py-3">Name</th>
                                    <th class="px-6 py-3">Currency</th>
                                    <th class="px-6 py-3 text-right">Balance</th>
                                    <th class="px-6 py-3">Status</th>
                                    <th class="px-6 py-3">Default</th>
                                    <th class="px-6 py-3 text-right">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @forelse($accounts as $account)
                                    <tr class="text-sm">
                                        <td class="px-6 py-4">{{ $account->name }}</td>
                                        <td class="px-6 py-4">{{ $account->currency }}</td>
                                        <td class="px-6 py-4 text-right {{ $account->balance < 0 ? 'text-red-600' : 'text-gray-900' }}">
                                            {{ number_format($account->balance, 2) }} {{ $account->currency }}
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs rounded-full {{ $account->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $account->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            @if($account->is_default)
                                                <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">Default</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 text-right space-x-3">
                                            <a href="{{ route('bank-accounts.show', $account) }}" class="text-blue-600 hover:text-blue-900">View</a>
                                            <a href="{{ route('bank-accounts.edit', $account) }}" class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                            @if(!$account->is_default)
                                                <form action="{{ route('bank-accounts.destroy', $account) }}" method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure?')">Delete</button>
                                                </form>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No bank accounts found
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 